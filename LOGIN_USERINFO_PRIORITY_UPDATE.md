# 微信登录用户信息优先级更新

## 修改概述

根据你的需求，修改了微信登录后用户头像和昵称的使用优先级逻辑：

**优先级规则：**
1. **优先使用**：接口返回的 `data.userInfo` 中的 `avatarUrl` 和 `nickname`
2. **备用方案**：当接口中的值为空时，才使用微信实时获取的值

## 接口返回数据结构

根据你提供的接口返回数据：

```json
{
  "code": 0,
  "msg": "成功", 
  "data": {
    "userId": 1,
    "ten": "...",
    "expireTime": 1755261192518,
    "userInfo": {
      "nickname": "ooki",
      "avatarUrl": "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132",
      "gender": 0,
      "status": 0,
      "lastLoginTime": 1754656392236,
      "createTime": 1752299046000
    },
    "openid": "on3U95Bntn51lW_QQ4PZvm2y1GHc",
    "sessionKey": "cBhZXLanjuRD4Uu6yA+3Fg=="
  }
}
```

## 修改的文件

### 1. `utils/auth.js`

**修改位置：** `saveLoginInfo` 方法（第181-225行）

**主要变更：**
- 添加了对后端返回的 `userInfo` 的解析
- 实现了优先级逻辑：
  ```javascript
  // 头像和昵称：优先使用后端返回的，为空时才使用微信实时获取的
  nickName: (backendUserInfo.nickname && backendUserInfo.nickname.trim()) ? 
            backendUserInfo.nickname : 
            (userInfo ? (userInfo.nickName || '微信用户') : '微信用户'),
  avatarUrl: (backendUserInfo.avatarUrl && backendUserInfo.avatarUrl.trim()) ? 
             backendUserInfo.avatarUrl : 
             (userInfo ? (userInfo.avatarUrl || '') : ''),
  ```
- 添加了详细的日志输出，方便调试

## 逻辑验证

已通过测试验证了以下场景：

### 场景1：后端有完整用户信息
- **后端返回：** `nickname: "ooki"`, `avatarUrl: "https://..."`
- **微信实时：** `nickName: "微信实时昵称"`, `avatarUrl: "https://wx-avatar-realtime.jpg"`
- **最终结果：** 使用后端的 `"ooki"` 和后端的头像URL ✅

### 场景2：后端用户信息为空
- **后端返回：** `nickname: ""`, `avatarUrl: ""`
- **微信实时：** `nickName: "微信实时昵称"`, `avatarUrl: "https://wx-avatar-realtime.jpg"`
- **最终结果：** 使用微信实时的昵称和头像 ✅

## 字段映射说明

| 数据源 | 昵称字段 | 头像字段 |
|--------|----------|----------|
| 后端接口 | `nickname` | `avatarUrl` |
| 微信实时 | `nickName` | `avatarUrl` |
| 最终保存 | `nickName` | `avatarUrl` |

## 调试信息

修改后的代码会在控制台输出详细的调试信息：

```
🎯 最终用户信息构建结果:
  昵称来源: 后端接口
  头像来源: 后端接口  
  最终昵称: ooki
  最终头像: https://thirdwx.qlogo.cn/mmopen/vi_32/...
```

## 注意事项

1. **字段名差异**：后端返回的是 `nickname`，但前端统一使用 `nickName`（驼峰命名）
2. **空值判断**：使用 `trim()` 方法确保空字符串也被视为无效值
3. **向下兼容**：如果后端没有返回 `userInfo` 对象，会自动降级使用微信实时数据
4. **页面显示**：WXML 中的显示逻辑无需修改，继续使用 `{{userInfo.nickName}}` 和 `{{userInfo.avatarUrl}}`

## 测试建议

建议在以下情况下测试：
1. 后端返回完整用户信息时
2. 后端返回空用户信息时  
3. 后端不返回 `userInfo` 字段时
4. 用户首次登录时
5. 用户重新登录时
