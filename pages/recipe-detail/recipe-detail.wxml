<!--pages/recipe-detail/recipe-detail.wxml-->
<navigation-bar title="{{recipeDetail.name || '菜谱详情'}}" back="{{true}}" color="black" background="#FFF"></navigation-bar>
<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-content">
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 错误状态 -->
  <view class="error-container" wx:elif="{{loadError}}">
    <view class="error-content">
      <text class="error-icon">😞</text>
      <text class="error-text">加载失败</text>
      <button class="retry-btn" bindtap="retryLoad">重试</button>
    </view>
  </view>

  <!-- 菜谱详情内容 -->
  <scroll-view class="recipe-content" scroll-y="true" enable-back-to-top="true">
    <!-- 添加顶部间距 -->
    <view class="header-spacer"></view>

    <!-- 菜谱主要信息卡片 -->
    <view class="recipe-main-card">
      <!-- 菜谱图片 -->
      <view class="recipe-image">
        <text class="recipe-emoji">🍜</text>
      </view>

      <!-- 菜谱基本信息 -->
      <view class="recipe-info">
        <text class="recipe-name">{{recipeDetail.name}}</text>

        <!-- 菜谱元数据 -->
        <!-- <view class="recipe-meta">
          <view class="meta-item">
            <text class="meta-icon">⏰</text>
            <text class="meta-text">{{recipeDetail.cookingTime || '15'}}分钟</text>
          </view>
          <view class="meta-item">
            <text class="meta-icon">👨‍🍳</text>
            <text class="meta-text">{{recipeDetail.difficultyLevelDesc || '★★级'}}</text>
          </view>
        </view> -->

        <!-- 评分和人数 -->
        <view class="recipe-stats">
          <view class="stat-item">
            <text class="star-icon">⭐</text>
            <text class="stat-value">{{recipeDetail.difficultyLevel || '3'}}</text>
          </view>
          <view class="stat-item">
            <text class="people-icon">👥</text>
            <text class="stat-value">{{recipeDetail.viewCount || '1'}}人做过</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 所需食材 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">所需食材</text>
      </view>
      <view class="ingredients-container">
        <view class="ingredient-item" wx:for="{{ingredientsList}}" wx:key="id">
          <view class="ingredient-status">
            <text class="status-icon {{item.hasIngredient ? 'has' : 'missing'}}">{{item.hasIngredient ? '✓' : '✗'}}</text>
          </view>
          <view class="ingredient-info">
            <text class="ingredient-name">{{item.ingredientName}}</text>
          </view>
          <view class="ingredient-quantity">
            <text class="quantity-text">{{item.quantity}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 营养信息 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">营养信息</text>
      </view>
      <view class="nutrition-container">
        <view class="nutrition-item">
          <text class="nutrition-value calories">{{nutritionInfo.calories || '320'}}</text>
          <text class="nutrition-label">卡路里</text>
        </view>
        <view class="nutrition-item">
          <text class="nutrition-value protein">{{nutritionInfo.protein || '18g'}}</text>
          <text class="nutrition-label">蛋白质</text>
        </view>
        <view class="nutrition-item">
          <text class="nutrition-value carbs">{{nutritionInfo.carbs || '45g'}}</text>
          <text class="nutrition-label">碳水</text>
        </view>
        <view class="nutrition-item">
          <text class="nutrition-value fat">{{nutritionInfo.fat || '8g'}}</text>
          <text class="nutrition-label">脂肪</text>
        </view>
      </view>
    </view>

    <!-- 制作步骤 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">制作步骤</text>
      </view>
      <view class="steps-list">
        <view class="step-item" wx:for="{{stepsList}}" wx:key="id">
          <view class="step-number">{{item.stepOrder}}</view>
          <view class="step-content">
            <text class="step-text">{{item.description}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部间距 -->
    <view class="bottom-spacing"></view>
  </scroll-view>
</view>
